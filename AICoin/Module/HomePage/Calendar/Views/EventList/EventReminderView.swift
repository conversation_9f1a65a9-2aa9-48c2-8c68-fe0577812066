//
//  EventReminderView.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-22.
//  Copyright © 2024 AICoin. All rights reserved.
//

import SnapKit
import UIKit

class EventReminderView: UIView {

    // MARK: - 常量

    private struct Constants {
        // 字体 - 将Figma像素值除以2转换为iOS点单位
        static let titleFont = UIFont.systemFont(ofSize: 16)
        static let sectionTitleFont = UIFont.systemFont(ofSize: 14, weight: .medium)
        static let normalTextFont = UIFont.systemFont(ofSize: 14)
        static let buttonTextFont = UIFont.systemFont(ofSize: 10)
        static let noteTextFont = UIFont.systemFont(ofSize: 10)
        static let actionButtonFont = UIFont.systemFont(ofSize: 16)
        static let typeFont = UIFont.systemFont(ofSize: 12)

        // 布局
        static let cornerRadius: CGFloat = 16
        static let buttonCornerRadius: CGFloat = 4
        static let actionButtonHeight: CGFloat = 43  // 86/2
        static let containerPadding = UIEdgeInsets(top: 4, left: 16, bottom: 16, right: 16)  // 调整padding
        static let buttonHeight: CGFloat = 30  // 56/2
    }

    // MARK: - 属性

    var eventModel: CalendarEventModel?

    // 当前选中的提醒时间（秒数）
    private var selectedReminderTime: Int = 3600  // 默认提前60分钟

    // MARK: - UI组件

    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.themeColor(day: 0xFFFFFF, night: 0x1F2126)
        view.layer.cornerRadius = Constants.cornerRadius
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]  // 只设置上方圆角
        view.clipsToBounds = true
        return view
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.titleFont
        label.textColor = DynamicHelper.themeColor(day: 0x7A8899, night: 0x7A8899)
        label.text = "设置提醒"
        return label
    }()

    // 分隔线1
    private lazy var divider1: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.themeColor(day: 0xDEE1E5, night: 0x313036)
        return view
    }()

    // 提醒事件部分
    private lazy var eventTitleLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.sectionTitleFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.text = "提醒事件"
        return label
    }()

    private lazy var eventContentLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.sectionTitleFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        label.text = "美联储主席鲍威尔在杰克逊霍尔年会上就经济前景发表讲话威尔在杰克逊霍尔年会上就经济前景发表讲话"
        return label
    }()

    // 分隔线2
    private lazy var divider2: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.themeColor(day: 0xDEE1E5, night: 0x313036)
        return view
    }()

    // 通知方式部分
    private lazy var notificationMethodLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.sectionTitleFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.text = "通知方式"
        return label
    }()

    // PC通知
    private lazy var pcNotificationView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var pcNotificationLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.normalTextFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.text = "PC通知"
        return label
    }()

    let pcNotificationSwitch = AICSwitch()

    // APP通知
    private lazy var appNotificationView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var appNotificationLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.normalTextFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.text = "APP通知"
        return label
    }()

    let appNotificationSwitch = AICSwitch()

    // 邮件通知
    private lazy var emailNotificationView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var emailNotificationLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.normalTextFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.text = "邮件通知"
        return label
    }()

    let emailNotificationSwitch = AICSwitch()

    // 同步到手机日历
    private lazy var calendarSyncView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var calendarSyncLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.normalTextFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.text = "同步到手机日历"
        return label
    }()

    let calendarSyncSwitch = AICSwitch()

    // 分隔线3
    private lazy var divider3: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.themeColor(day: 0xDEE1E5, night: 0x313036)
        return view
    }()

    // 提醒时间部分
    private lazy var reminderTimeLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.sectionTitleFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.text = "提醒时间"
        return label
    }()

    private lazy var eventTimeLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.sectionTitleFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.text = "将在 2024-08-23  21：00 提醒您"
        return label
    }()

    // 提前时间选择部分
    private lazy var timeOptionsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12  // 行间距，Figma 24/2
        return stackView
    }()

    // 按钮数组，用于方便管理所有时间选项按钮
    private var timeOptionButtons: [UIButton] = []

    // 分隔线4
    private lazy var divider4: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.themeColor(day: 0xDEE1E5, night: 0x313036)
        return view
    }()

    // 备注部分
    private lazy var noteLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.sectionTitleFont
        label.textColor = UIColor.baseCurrentCellTitleColor
        label.text = "备注"
        return label
    }()

    private lazy var noteInputField: UITextField = {
        let textField = UITextField()
        textField.font = Constants.noteTextFont
        textField.textColor = UIColor.baseCurrentCellTitleColor
        textField.textAlignment = .right
        textField.attributedPlaceholder = NSAttributedString(
            string: "（选填）",
            attributes: [
                .font: Constants.noteTextFont,
                .foregroundColor: UIColor.baseTheme.current.searchBarPlaceHolderColor,
            ])
        textField.delegate = self
        textField.addTarget(self, action: #selector(noteTextChanged), for: .editingChanged)
        return textField
    }()

    private lazy var noteCountLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.noteTextFont
        label.textColor = UIColor.baseTheme.current.searchBarPlaceHolderColor
        label.text = "0/10"
        label.textAlignment = .right
        return label
    }()

    // 分隔线5
    private lazy var divider5: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.themeColor(day: 0xDEE1E5, night: 0x313036)
        return view
    }()

    // 底部按钮部分
    private lazy var buttonStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 16  // 32/2
        return stackView
    }()

    private lazy var cancelButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("取消", for: .normal)
        button.setTitleColor(UIColor.baseCurrentMainColor, for: .normal)
        button.titleLabel?.font = Constants.actionButtonFont
        button.layer.cornerRadius = Constants.buttonCornerRadius
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.baseCurrentMainColor.cgColor
        return button
    }()

    private lazy var saveButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("保存", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = Constants.actionButtonFont
        button.backgroundColor = UIColor.baseCurrentMainColor
        button.layer.cornerRadius = Constants.buttonCornerRadius
        return button
    }()

    private lazy var typeLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.typeFont
        label.textColor = UIColor.baseCurrentMainColor
        label.textAlignment = .center
        label.layer.cornerRadius = 4
        label.clipsToBounds = true
        label.backgroundColor = UIColor.baseCurrentMainColor.withAlphaComponent(0.1)
        return label
    }()

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    // MARK: - 设置视图

    private func setupViews() {
        // 添加主容器
        addSubview(containerView)

        // 添加标题
        containerView.addSubview(titleLabel)

        // 添加分隔线和事件内容
        containerView.addSubview(divider1)
        containerView.addSubview(eventTitleLabel)
        containerView.addSubview(eventContentLabel)

        // 添加通知方式部分
        containerView.addSubview(divider2)
        containerView.addSubview(notificationMethodLabel)

        // PC通知
        containerView.addSubview(pcNotificationView)
        pcNotificationView.addSubview(pcNotificationLabel)
        pcNotificationView.addSubview(pcNotificationSwitch)

        // APP通知
        containerView.addSubview(appNotificationView)
        appNotificationView.addSubview(appNotificationLabel)
        appNotificationView.addSubview(appNotificationSwitch)

        // 邮件通知
        containerView.addSubview(emailNotificationView)
        emailNotificationView.addSubview(emailNotificationLabel)
        emailNotificationView.addSubview(emailNotificationSwitch)

        // 同步到手机日历
        containerView.addSubview(calendarSyncView)
        calendarSyncView.addSubview(calendarSyncLabel)
        calendarSyncView.addSubview(calendarSyncSwitch)

        // 添加提醒时间部分
        containerView.addSubview(divider3)
        containerView.addSubview(reminderTimeLabel)
        containerView.addSubview(eventTimeLabel)

        // 添加时间选择部分
        containerView.addSubview(timeOptionsStackView)
        // 创建时间选项按钮
        createTimeOptionButtons()

        // 添加备注部分
        containerView.addSubview(divider4)
        containerView.addSubview(noteLabel)
        containerView.addSubview(noteInputField)
        containerView.addSubview(noteCountLabel)
        containerView.addSubview(divider5)

        // 添加底部按钮
        containerView.addSubview(buttonStackView)
        buttonStackView.addArrangedSubview(cancelButton)
        buttonStackView.addArrangedSubview(saveButton)

        // 设置约束
        setupConstraints()

        // 设置按钮动作
        setupActions()
    }

    private func createTimeOptionButtons() {
        let timeOptions = ["提前3天", "提前2天", "提前1天", "提前60分", "提前30分", "提前15分", "提前5分", "发生时"]
        // 时间选项对应的秒数值
        let timeValues = [259200, 172800, 86400, 3600, 1800, 900, 300, 0]  // 3天、2天、1天、60分、30分、15分、5分、0秒

        timeOptionButtons.removeAll()
        timeOptionsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        let maxPerRow = 4
        var rowStack: UIStackView?
        for (idx, option) in timeOptions.enumerated() {
            if idx % maxPerRow == 0 {
                rowStack = UIStackView()
                rowStack?.axis = .horizontal
                rowStack?.distribution = .fillEqually
                rowStack?.spacing = 8  // Figma 16/2
                timeOptionsStackView.addArrangedSubview(rowStack!)
            }
            let button = createTimeOptionButton(title: option)
            button.tag = timeValues[idx]  // 将时间值存储在tag中
            rowStack?.addArrangedSubview(button)
            timeOptionButtons.append(button)
        }
        // 默认选中"提前60分"
        if let index = timeOptions.firstIndex(of: "提前60分") {
            selectTimeOption(at: index)
            selectedReminderTime = timeValues[index]
        }
    }

    private func createTimeOptionButton(title: String) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.setTitleColor(UIColor.baseCurrentCellTitleColor, for: .normal)
        button.titleLabel?.font = Constants.buttonTextFont
        button.backgroundColor = UIColor.themeColor(day: 0xFFFFFF, night: 0x1F2126)
        button.layer.cornerRadius = Constants.buttonCornerRadius
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.themeColor(day: 0xD9D9D9, night: 0xD9D9D9).cgColor
        button.addTarget(self, action: #selector(timeOptionTapped(_:)), for: .touchUpInside)
        button.snp.makeConstraints { make in
            make.height.equalTo(Constants.buttonHeight)
        }
        return button
    }

    private func setupConstraints() {
        // 主容器约束 - 移除固定高度，让内容撑开
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
        }

        // 标题约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)  // 32/2，原来是handleView下方
            make.left.right.equalToSuperview().inset(16)  // 32/2
        }

        // 分隔线1约束
        divider1.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)  // 24/2
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(0.5)
        }

        // 事件内容约束
        eventTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(divider1.snp.bottom).offset(12)  // 24/2
            make.left.equalToSuperview().offset(16)  // 32/2
        }

        eventContentLabel.snp.makeConstraints { make in
            make.top.equalTo(eventTitleLabel)  // 改为顶部对齐
            make.left.equalTo(eventTitleLabel.snp.right).offset(16)  // 在eventTitleLabel右边偏移16
            make.right.equalToSuperview().offset(-16)  // -32/2
        }

        // 分隔线2约束
        divider2.snp.makeConstraints { make in
            make.top.equalTo(eventContentLabel.snp.bottom).offset(12)  // 24/2
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(0.5)
        }

        // 通知方式约束
        notificationMethodLabel.snp.makeConstraints { make in
            make.top.equalTo(divider2.snp.bottom).offset(12)  // 24/2
            make.left.equalToSuperview().offset(16)  // 32/2
        }

        // PC通知约束
        pcNotificationView.snp.makeConstraints { make in
            make.top.equalTo(notificationMethodLabel.snp.bottom).offset(14)  // 24/2
            make.left.right.equalToSuperview().inset(16)  // 32/2
            make.height.equalTo(22)  // 44/2
        }

        pcNotificationLabel.snp.makeConstraints { make in
            make.left.centerY.equalToSuperview()
        }

        pcNotificationSwitch.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()

        }

        // APP通知约束
        appNotificationView.snp.makeConstraints { make in
            make.top.equalTo(pcNotificationView.snp.bottom).offset(14)  // 16/2
            make.left.right.equalToSuperview().inset(16)  // 32/2
            make.height.equalTo(22)  // 44/2
        }

        appNotificationLabel.snp.makeConstraints { make in
            make.left.centerY.equalToSuperview()
        }

        appNotificationSwitch.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
        }

        // 邮件通知约束
        emailNotificationView.snp.makeConstraints { make in
            make.top.equalTo(appNotificationView.snp.bottom).offset(14)  // 16/2
            make.left.right.equalToSuperview().inset(16)  // 32/2
            make.height.equalTo(22)  // 44/2
        }

        emailNotificationLabel.snp.makeConstraints { make in
            make.left.centerY.equalToSuperview()
        }

        emailNotificationSwitch.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
        }

        // 同步到手机日历约束
        calendarSyncView.snp.makeConstraints { make in
            make.top.equalTo(emailNotificationView.snp.bottom).offset(14)  // 16/2
            make.left.right.equalToSuperview().inset(16)  // 32/2
            make.height.equalTo(22)  // 44/2
        }

        calendarSyncLabel.snp.makeConstraints { make in
            make.left.centerY.equalToSuperview()
        }

        calendarSyncSwitch.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
        }

        // 分隔线3约束
        divider3.snp.makeConstraints { make in
            make.top.equalTo(calendarSyncView.snp.bottom).offset(12)  // 24/2
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(0.5)
        }

        // 提醒时间约束
        reminderTimeLabel.snp.makeConstraints { make in
            make.top.equalTo(divider3.snp.bottom).offset(14)  // 24/2
            make.left.equalToSuperview().offset(16)  // 32/2
        }

        eventTimeLabel.snp.makeConstraints { make in
            make.top.equalTo(reminderTimeLabel)
            make.left.equalTo(reminderTimeLabel.snp.right).offset(16)  // 在eventTitleLabel右边偏移16
            make.right.equalToSuperview().offset(-16)  // -32/2
        }

        // 时间选择器约束
        timeOptionsStackView.snp.makeConstraints { make in
            make.top.equalTo(reminderTimeLabel.snp.bottom).offset(14)  // 16/2
            make.left.right.equalToSuperview().inset(16)  // 32/2
        }

        // 分隔线4约束
        divider4.snp.makeConstraints { make in
            make.top.equalTo(timeOptionsStackView.snp.bottom).offset(12)  // 24/2
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(0.5)
        }

        // 备注部分约束
        noteLabel.snp.makeConstraints { make in
            make.top.equalTo(divider4.snp.bottom).offset(12)  // 24/2
            make.left.equalToSuperview().offset(16)  // 32/2
        }

        noteInputField.snp.makeConstraints { make in
            make.top.equalTo(noteLabel).offset(-2)
            make.right.equalTo(noteCountLabel.snp.left).offset(-8)
            make.height.equalTo(28)
            make.width.equalTo(180)  // Figma宽度，或可自适应
        }

        noteCountLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalTo(noteInputField)
            make.width.equalTo(40)
        }

        divider5.snp.makeConstraints { make in
            make.top.equalTo(noteLabel.snp.bottom).offset(8)
            make.right.equalToSuperview().inset(16)
            make.left.equalTo(noteLabel.snp.right).offset(32)
            make.height.equalTo(0.5)
        }

        // 底部按钮约束
        buttonStackView.snp.makeConstraints { make in
            make.top.equalTo(divider5.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalTo(self.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.centerX.equalToSuperview()
            make.height.equalTo(Constants.actionButtonHeight)
        }

        cancelButton.snp.makeConstraints { make in
            make.width.equalTo(165)  // 330/2
        }

        saveButton.snp.makeConstraints { make in
            make.width.equalTo(165)  // 330/2
        }
    }

    private func setupActions() {

        cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        saveButton.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
    }

    // MARK: - 事件处理

    @objc private func timeOptionTapped(_ sender: UIButton) {
        if let index = timeOptionButtons.firstIndex(of: sender) {
            selectTimeOption(at: index)
            selectedReminderTime = sender.tag  // 更新选中的提醒时间
        }
    }

    private func selectTimeOption(at index: Int) {
        for (i, button) in timeOptionButtons.enumerated() {
            if i == index {
                // 选中状态
                button.backgroundColor = UIColor.baseCurrentMainColor.withAlphaComponent(0.1)
                button.layer.borderColor = UIColor.baseCurrentMainColor.cgColor
                button.setTitleColor(UIColor.baseCurrentMainColor, for: .normal)
            } else {
                // 未选中状态，背景色改为弹窗背景色
                button.backgroundColor = UIColor.themeColor(day: 0xFFFFFF, night: 0x1F2126)  // 主题适配
                button.layer.borderColor =
                    UIColor.themeColor(day: 0xD9D9D9, night: 0xD9D9D9).cgColor
                button.setTitleColor(UIColor.baseCurrentCellTitleColor, for: .normal)
            }
        }
    }

    @objc private func cancelButtonTapped() {
        // 处理取消按钮点击事件
        viewController?.dismiss(animated: true, completion: nil)
    }

    @objc private func saveButtonTapped() {
        // 处理保存按钮点击事件
        guard let event = eventModel else {
            viewController?.dismiss(animated: true, completion: nil)
            return
        }

        // 禁用保存按钮，防止重复提交
        saveButton.isEnabled = false
        saveButton.setTitle("保存中...", for: .normal)

        // 调用提醒设置API
        saveEventReminder(event: event) { [weak self] success, error in
            DispatchQueue.main.async {
                self?.saveButton.isEnabled = true
                self?.saveButton.setTitle("保存", for: .normal)

                if success {
                    // 保存成功，关闭弹窗
                    self?.viewController?.dismiss(animated: true, completion: nil)
                } else {
                    // 保存失败，显示错误信息
                    let errorMessage = error?.localizedDescription ?? "保存失败，请重试"
                    // 这里可以添加Toast提示
                    print("保存提醒失败: \(errorMessage)")
                }
            }
        }
    }

    @objc private func noteTextChanged() {
        let count = noteInputField.text?.count ?? 0
        noteCountLabel.text = "\(count)/10"
    }

    // MARK: - 公共方法

    func configure(with event: CalendarEventModel) {
        self.eventModel = event
        self.eventContentLabel.text = event.title

        // 设置时间提醒
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        self.eventTimeLabel.text = "将在 \(formatter.string(from: event.date)) 提醒您"

        // 更新UI
        setNeedsLayout()
    }

    // MARK: - 网络请求

    /// 保存事件提醒设置
    /// - Parameters:
    ///   - event: 事件模型
    ///   - completion: 完成回调
    private func saveEventReminder(
        event: CalendarEventModel, completion: @escaping (Bool, Error?) -> Void
    ) {
        // 调用CalendarEventManager的方法
        CalendarEventManager.shared.saveEventReminder(
            eventId: event.id,
            startDate: event.startDate,
            reminderTime: selectedReminderTime,
            isApp: appNotificationSwitch.isOn(),
            isPc: pcNotificationSwitch.isOn(),
            isEmail: emailNotificationSwitch.isOn(),
            remarks: noteInputField.text ?? "",
            type: event.type
        ) { success, error in
            if success {
                UIApplication.shared.mainWindow?.makeToast(
                    "提醒设置成功", duration: 0.5, position: "center")
                completion(true, nil)
            } else {
                let errorMessage = error?.localizedDescription ?? "保存失败，请重试"
                UIApplication.shared.mainWindow?.makeToast(
                    errorMessage, duration: 1, position: "center")
                completion(false, error)
            }
        }
    }
}

// MARK: - UITextFieldDelegate & 备注输入处理
extension EventReminderView: UITextFieldDelegate {
    func textField(
        _ textField: UITextField, shouldChangeCharactersIn range: NSRange,
        replacementString string: String
    ) -> Bool {
        // 限制最大10字符
        let current = textField.text ?? ""
        guard let stringRange = Range(range, in: current) else { return false }
        let updated = current.replacingCharacters(in: stringRange, with: string)
        return updated.count <= 10
    }
}

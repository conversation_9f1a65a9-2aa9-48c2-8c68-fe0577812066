//
//  EventListView.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import SnapKit
import UIKit

class EventListView: UIView {

    // MARK: - 属性

    private var viewModel: CalendarViewModel!

    // 事件选择回调
    var onEventSelected: ((CalendarEventModel) -> Void)?
    // 事件提醒回调
    var onEventReminder: ((CalendarEventModel) -> Void)?
    // 标签点击回调
    var onTagTapped: ((CalendarRelatedEntrance) -> Void)?

    // MARK: - UI组件

    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = UIColor.baseTheme.current.bgColor
        tableView.separatorStyle = .singleLine
        tableView.separatorColor = UIColor.baseTheme.current.bgColor
        tableView.separatorInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        tableView.register(CalendarEventCell.self, forCellReuseIdentifier: "CalendarEventCell")
        tableView.delegate = self
        tableView.dataSource = self
        tableView.rowHeight = UITableView.automaticDimension
        tableView.estimatedRowHeight = 110
        tableView.showsVerticalScrollIndicator = false  // 隐藏垂直滚动条
        // tableView.bounces = false

        return tableView
    }()

    private lazy var noEventLabel: UILabel = {
        let label = UILabel()
        label.text = "当前日期暂无事件"
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor.baseTheme.current.cellSubtitleColor
        label.isHidden = true
        return label
    }()

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    // MARK: - 设置

    private func setupViews() {
        backgroundColor = UIColor.baseTheme.current.bgColor

        addSubview(tableView)
        addSubview(noEventLabel)

        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        noEventLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
        }
    }

    // MARK: - 公共方法

    func configure(with viewModel: CalendarViewModel) {
        self.viewModel = viewModel
        setupBindings()
        updateUI()
    }

    func reloadData() {
        tableView.reloadData()
        updateUI()
    }

    // MARK: - 私有方法

    private func setupBindings() {
        viewModel.onEventsUpdated = { [weak self] in
            self?.tableView.reloadData()
            self?.updateUI()
        }
    }

    private func updateUI() {
        if viewModel.hasEvents {
            tableView.isHidden = false
            noEventLabel.isHidden = true
        } else {
            tableView.isHidden = true
            noEventLabel.isHidden = false
        }
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension EventListView: UITableViewDataSource, UITableViewDelegate {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.events.count
    }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell =
            tableView.dequeueReusableCell(withIdentifier: "CalendarEventCell", for: indexPath)
            as! CalendarEventCell

        let events = viewModel.events
        if indexPath.row < events.count {
            let event = events[indexPath.row]
            cell.configure(with: event)
            // 设置提醒按钮回调
            cell.onReminderTapped = { [weak self] in
                self?.onEventReminder?(event)
            }
            // 设置标签点击回调
            cell.onTagTapped = { [weak self] entrance in
                self?.onTagTapped?(entrance)
            }
        }

        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath)
        -> CGFloat
    {
        return 110  // 估计高度
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        // 触发事件选择回调
        let events = viewModel.events
        if indexPath.row < events.count {
            onEventSelected?(events[indexPath.row])
        }
    }
}

// MARK: - 日历事件单元格
class CalendarEventCell: UITableViewCell {

    // MARK: - 常量

    private struct Constants {
        // 字体
        static let titleFont = UIFont.systemFont(ofSize: 16, weight: .medium)
        static let typeFont = UIFont.systemFont(ofSize: 12)
        static let timeFont = UIFont.systemFont(ofSize: 14)

        // 布局
        static let contentPadding = UIEdgeInsets(top: 12, left: 16, bottom: 12, right: 16)

        // 圆角
        static let cornerRadius: CGFloat = 8
    }

    // MARK: - 回调
    var onReminderTapped: (() -> Void)?
    // 标签点击回调
    var onTagTapped: ((CalendarRelatedEntrance) -> Void)?
    // 存储当前事件的relatedEntrances
    private var relatedEntrances: [CalendarRelatedEntrance] = []

    // MARK: - UI组件

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.titleFont
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        return label
    }()

    // 替换typeLabel为标签容器
    private let tagsScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        return scrollView
    }()

    private let tagsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.alignment = .center
        stackView.distribution = .fill
        stackView.spacing = 8
        return stackView
    }()

    private let timeLabel: UILabel = {
        let label = UILabel()
        label.font = Constants.timeFont
        label.textColor = UIColor.themeColor(day: 0x757575, night: 0xC3C7D9)
        return label
    }()

    private let reminderButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.home.image(name: "icon_bell"), for: .normal)
        button.contentMode = .scaleAspectFit
        button.tintColor = UIColor.themeColor(day: 0x757575, night: 0xC3C7D9)
        return button
    }()

    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.designKit.secondaryBackgroundNew
        return view
    }()

    private let headerView: UIView = {
        let view = UIView()
        return view
    }()

    private let footerView: UIView = {
        let view = UIView()
        return view
    }()

    // MARK: - 初始化

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 设置视图

    private func setupViews() {
        selectionStyle = .none
        backgroundColor = .clear

        // 确保分割线能够正确显示
        preservesSuperviewLayoutMargins = false
        layoutMargins = .zero

        // 添加视图层次结构
        super.contentView.addSubview(containerView)

        // 添加主要部分
        containerView.addSubview(headerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(footerView)

        // 添加子视图到各个部分
        headerView.addSubview(timeLabel)
        headerView.addSubview(reminderButton)

        // 将tagsScrollView添加到footerView
        footerView.addSubview(tagsScrollView)
        tagsScrollView.addSubview(tagsStackView)

        // 设置容器视图约束 - 删除边距
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 设置各部分的约束
        headerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(30)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(Constants.contentPadding.left)
            make.right.equalToSuperview().offset(-Constants.contentPadding.right)
        }

        footerView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview()
            make.height.equalTo(40)
            make.bottom.equalToSuperview()
        }

        // 设置头部子视图约束
        timeLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(Constants.contentPadding.left)
            make.centerY.equalToSuperview()
        }

        reminderButton.snp.makeConstraints { make in
            make.left.equalTo(timeLabel.snp.right).offset(8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        // 设置tagsScrollView约束
        tagsScrollView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(Constants.contentPadding.left)
            make.right.equalToSuperview().offset(-Constants.contentPadding.right)
            make.top.bottom.equalToSuperview()
        }

        // 设置tagsStackView约束
        tagsStackView.snp.makeConstraints { make in
            make.left.top.right.bottom.equalToSuperview()
            make.height.equalTo(24)

            // 不设置right约束，允许内容超出
        }

        // 添加提醒按钮点击事件
        reminderButton.addTarget(self, action: #selector(reminderButtonTapped), for: .touchUpInside)
    }

    // 创建标签方法
    private func createTagLabel(with entrance: CalendarRelatedEntrance) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .themeColor(day: 0xF9FAFC, night: 0x313036)
        containerView.layer.cornerRadius = 4
        containerView.clipsToBounds = true

        let label = UILabel()
        label.font = Constants.typeFont
        label.textColor = UIColor(hexString: "#398EFF")
        label.text = entrance.displayText

        containerView.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(8)
            make.top.bottom.equalToSuperview().inset(4)
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(tagTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)
        containerView.isUserInteractionEnabled = true
        // 使用tag存储索引
        containerView.tag = tagsStackView.arrangedSubviews.count

        return containerView
    }

    // 处理标签点击
    @objc private func tagTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let index = view.tag
        if index < relatedEntrances.count {
            onTagTapped?(relatedEntrances[index])
        }
    }

    @objc private func reminderButtonTapped() {
        // 提醒按钮点击事件
        onReminderTapped?()
    }

    // MARK: - 配置

    func configure(with event: CalendarEventModel) {
        // 设置标题
        titleLabel.text = event.title

        // 根据是否是重要事件设置标题颜色
        titleLabel.textColor =
            event.computedIsImportant
            ? UIColor.themeColor(day: 0xEB4236, night: 0xEB4236)
            : UIColor.themeColor(day: 0x292D33, night: 0xC3C7D9)

        // 设置时间 (格式为HH:mm)
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        if event.isPending {
            timeLabel.text = "待定"
        } else {
            timeLabel.text = formatter.string(from: event.date)
        }

        // 保存relatedEntrances
        self.relatedEntrances = event.relatedEntrances

        // 清除现有标签
        tagsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // 创建并添加标签
        for entrance in event.relatedEntrances {
            let tagView = createTagLabel(with: entrance)
            tagsStackView.addArrangedSubview(tagView)
        }

        // 保持footerView高度为40，无论是否有标签
        tagsScrollView.isHidden = event.relatedEntrances.isEmpty

        // 更新提醒按钮颜色
        updateReminderButtonColor(isRemind: event.isRemind)
    }

    /// 更新提醒按钮颜色
    private func updateReminderButtonColor(isRemind: Bool) {
        if isRemind {
            // 已设置提醒：使用主题色
            reminderButton.setImage(UIImage.home.image(name: "icon_bell")?.byTintColor(UIColor.baseCurrentMainColor), for: .normal)
        }
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        titleLabel.text = nil
        timeLabel.text = nil
        relatedEntrances = []
        // 清除所有标签
        tagsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
    }
}
